﻿"""
本模块负责处理所有与"通告"相关的异步数据库操作。
包括通告的增、删、改、查以及列表获取等功能。
所有函数均为异步函数，设计为与异步Web框架（如FastAPI）集成。
"""
import json
import traceback
from datetime import datetime
from typing import List, Dict, Any, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
# 导入统一日志系统
from 日志 import 系统日志器, 错误日志器


async def 异步获取通告列表(
    页码: int = 1, 
    每页数量: int = 10, 
    类型: Optional[str] = None, 
    # 只获取已发布: bool = False, # 由新的 状态 参数处理
    # 不返回内容: bool = True,   # 前端目前不关注这个，且新逻辑是获取所有字段然后按需处理
    标题: Optional[str] = None, 
    状态: Optional[int] = None, 
    创建时间开始: Optional[datetime] = None,
    创建时间结束: Optional[datetime] = None,
    排序字段: Optional[str] = None,
    排序顺序: Optional[str] = None
) -> Dict[str, Any]:
    """异步获取通告列表数据，支持分页、筛选和排序

    参数:
    页码: int - 当前页码，默认为1
    每页数量: int - 每页显示记录数，默认为10
    类型: Optional[str] - 可选的通告类型筛选
    标题: Optional[str] - 按标题模糊筛选
    状态: Optional[int] - 按状态筛选 (0: 草稿, 1: 已发布)
    创建时间开始: Optional[datetime] - 创建时间范围开始
    创建时间结束: Optional[datetime] - 创建时间范围结束
    排序字段: Optional[str] - 排序字段
    排序顺序: Optional[str] - 排序顺序 (ascend/descend)

    返回值:
    Dict[str, Any]: 包含通告列表数据和分页信息
                   {'列表': List[Dict], '总数': int, '总页数': int, '当前页': int}
    """
    try:
        错误日志器.info(f"数据层-开始获取通告列表: 页码={页码}, 每页数量={每页数量}, 类型={类型}, 标题={标题}, 状态={状态}, 排序={排序字段} {排序顺序}")
        偏移量 = (页码 - 1) * 每页数量
        
        # 获取表的所有字段以确保查询的稳健性
        # (原有的表结构检查和修复逻辑可以保留，这里不重复展示，假设表结构是正确的)
        # ... 原表结构检查和修复代码 ...

        字段查询字符串 = "*" # 简化为获取所有字段，后续Python处理内容解析
        
        # 构建查询条件
        条件列表 = []
        查询参数 = []
        
        参数索引 = 1
        if 类型:
            条件列表.append(f"类型 = ${参数索引}")
            查询参数.append(类型)
            参数索引 += 1

        if 标题:
            条件列表.append(f"标题 LIKE ${参数索引}")
            查询参数.append(f"%{标题}%")
            参数索引 += 1

        if 状态 is not None: # 0也是有效状态
            条件列表.append(f"已发布 = ${参数索引}") # 假设数据库字段是 已发布
            查询参数.append(状态)  # 直接使用原始值，不转换为bool
            参数索引 += 1

        if 创建时间开始:
            条件列表.append(f"创建时间 >= ${参数索引}")
            查询参数.append(创建时间开始)
            参数索引 += 1

        if 创建时间结束:
            条件列表.append(f"创建时间 <= ${参数索引}")
            查询参数.append(创建时间结束)
            参数索引 += 1
        
        # 有效期条件 (原逻辑保留) - 注释掉，因为管理员应该能看到所有通告
        # 条件列表.append("(开始时间 IS NULL OR 开始时间 <= NOW())")
        # 条件列表.append("(结束时间 IS NULL OR 结束时间 >= NOW())")
        
        条件语句 = f" WHERE {' AND '.join(条件列表)}" if 条件列表 else ""
        
        # 查询总记录数
        总数查询 = f"SELECT COUNT(*) as 总数 FROM 通告{条件语句}"
        错误日志器.debug(f"数据层-执行总数查询: {总数查询}, 参数: {查询参数}")
        总数结果 = await 异步连接池实例.执行查询(总数查询, 查询参数)
        总数 = 总数结果[0]['总数'] if 总数结果 else 0
        
        总页数 = (总数 + 每页数量 - 1) // 每页数量 if 总数 > 0 else 1
        
        # 处理排序
        order_by_子句 = " ORDER BY 更新时间 DESC" # 默认排序
        if 排序字段:
            有效的排序字段 = { # 白名单
                'id': 'id', '类型': '类型', '标题': '标题', 
                '已发布': '已发布', '重要性': '重要性',
                '开始时间': '开始时间', '结束时间': '结束时间', 
                '发布时间': '发布时间', '创建时间': '创建时间', '更新时间': '更新时间'
            }
            if 排序字段 in 有效的排序字段:
                db_sort_field = 有效的排序字段[排序字段]
                sort_direction = "ASC" if 排序顺序 and 排序顺序.lower() == 'ascend' else "DESC"
                order_by_子句 = f" ORDER BY {db_sort_field} {sort_direction}"

        查询语句 = f"""
            SELECT {字段查询字符串}
            FROM 通告{条件语句}
            {order_by_子句}
            LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        分页查询参数 = 查询参数 + [每页数量, 偏移量]
        
        错误日志器.debug(f"数据层-执行列表查询: {查询语句}, 参数: {分页查询参数}")
        通告列表 = await 异步连接池实例.执行查询(查询语句, 分页查询参数)
        
        # 处理内容字段，将JSON字符串转换为Python对象
        for 通告 in 通告列表:
            if 通告.get('内容'):
                try:
                    通告['内容'] = json.loads(通告['内容'])
                except json.JSONDecodeError:
                    错误日志器.error(f"数据层-通告ID {通告.get('id')} 内容解析失败")
                    通告['内容'] = [] # 解析失败则设为空列表
            else:
                通告['内容'] = [] # 内容为空也设为空列表

            # 计算通告是否有效 (原逻辑保留)
            通告['是否有效'] = True # 默认有效
            开始时间 = 通告.get('开始时间')
            结束时间 = 通告.get('结束时间')
            if 开始时间 and 结束时间:
                now = datetime.now()
                # 确保时间字段是datetime对象
                if isinstance(开始时间, datetime) and isinstance(结束时间, datetime):
                    if now < 开始时间 or now > 结束时间:
                        通告['是否有效'] = False
        
        返回结果 = {
            '列表': 通告列表 or [],
            '总数': 总数,
            '总页数': 总页数,
            '当前页': 页码
        }
        
        错误日志器.info(f"数据层-成功获取通告列表: 总数={总数}, 总页数={总页数}")
        return 返回结果
    except Exception as e:
        错误日志器.error(f"数据层-异步获取通告列表失败: {e}", exc_info=True)
        return {'列表': [], '总数': 0, '总页数': 0, '当前页': 页码}

async def 异步获取通告详情(通告id: int, 只获取已发布: bool = False) -> Optional[Dict[str, Any]]:
    """异步获取单个通告的详细信息

    参数:
    通告id: int - 通告ID
    只获取已发布: bool - 是否只获取已发布的通告，默认为False

    返回值:
    Optional[Dict[str, Any]]: 包含通告详情的字典，若未找到则返回None
    """
    try:
        系统日志器.info(f"数据层-开始获取通告详情: ID={通告id}, 只获取已发布={只获取已发布}")
        
        # 首先检查表是否存在
        表检查查询 = "SHOW TABLES LIKE '通告'"
        系统日志器.debug(f"数据层-检查通告表是否存在: {表检查查询}")
        表检查结果 = await 异步连接池实例.执行查询(表检查查询, [])
        系统日志器.debug(f"数据层-表检查结果: {表检查结果}")
        
        if not 表检查结果:
            错误日志器.error("数据层-通告表不存在，无法获取通告详情")
            return None
        
        # 获取表结构
        表结构查询 = "DESCRIBE 通告"
        系统日志器.debug(f"数据层-获取通告表结构: {表结构查询}")
        表结构结果 = await 异步连接池实例.执行查询(表结构查询, [])
        系统日志器.debug(f"数据层-表结构结果: {表结构结果}")
        
        字段列表 = [字段['Field'] for 字段 in 表结构结果] if 表结构结果 else []
        系统日志器.info(f"数据层-通告详情查询 - 通告表字段列表: {字段列表}")
        
        if not 字段列表:
            错误日志器.error("数据层-通告表结构异常，无法获取字段列表")
            return None
            
        # 使用表中实际存在的字段
        字段查询字符串 = ", ".join(f'"{字段}"' for 字段 in 字段列表)
        查询条件 = "id = $1"
        查询参数 = [通告id]
        
        # 如果只获取已发布的通告，添加已发布条件
        if 只获取已发布 and '已发布' in 字段列表:
            查询条件 += " AND 已发布 = 1"
        
        查询语句 = f"""
            SELECT {字段查询字符串}
            FROM 通告
            WHERE {查询条件}
        """
        
        系统日志器.info(f"数据层-准备执行详情查询: {查询语句}, 参数: {查询参数}")
        结果 = None # 初始化结果变量
        try:
            结果 = await 异步连接池实例.执行查询(查询语句, 查询参数) # Execute query
            系统日志器.info(f"数据层-查询执行完毕，原始结果: {结果}") # 记录原始查询结果
        except Exception as query_err:
            错误日志器.error(f"数据层-执行通告详情查询时发生错误: {query_err}", exc_info=True)
            return None # 查询出错，返回 None
        
        if not 结果:
            系统日志器.warning(f"数据层-未找到指定通告 (查询结果为空): ID={通告id}") # Log not found
            return None # Return None if query result is empty
            
        系统日志器.info(f"数据层-查询成功，找到 {len(结果)} 条记录") 
        通告详情 = 结果[0] # Get the first row
        
        # 确保通告详情对象包含标准字段，即使数据库中不存在
        if '已发布' not in 字段列表:
            通告详情['已发布'] = False
        if '重要性' not in 字段列表:
            通告详情['重要性'] = 1
        if '开始时间' not in 字段列表:
            通告详情['开始时间'] = None
        if '结束时间' not in 字段列表:
            通告详情['结束时间'] = None
        if '发布时间' not in 字段列表:
            通告详情['发布时间'] = None
        
        # 处理内容字段，将JSON字符串转换为Python对象
        try:
            if 通告详情.get('内容'):
                系统日志器.debug(f"数据层-尝试解析通告内容 JSON: {通告详情.get('内容')}")
                通告详情['内容'] = json.loads(通告详情['内容'])
                系统日志器.debug("数据层-内容 JSON 解析成功")
            else:
                系统日志器.debug("数据层-通告内容为空，设置为空列表")
                通告详情['内容'] = []
                
            # 计算通告是否有效
            通告详情['是否有效'] = True
            开始时间 = 通告详情.get('开始时间')
            结束时间 = 通告详情.get('结束时间')
            if 开始时间 and 结束时间:
                from datetime import datetime
                现在 = datetime.now()
                # 确保时间字段是datetime对象
                if isinstance(开始时间, datetime) and isinstance(结束时间, datetime):
                    if 现在 < 开始时间 or 现在 > 结束时间:
                        通告详情['是否有效'] = False
        except json.JSONDecodeError as json_decode_err:
            错误日志器.error(f"数据层-通告ID {通告id} 内容解析失败: {json_decode_err}")
            通告详情['内容'] = [] # 解析失败也设置为空列表
        
        系统日志器.info(f"数据层-成功获取并处理通告详情: ID={通告id}")
        return 通告详情 # Return the dictionary
    except Exception as e:
        错误日志器.error(f"数据层-异步获取通告详情失败: ID={通告id}, 错误={e}", exc_info=True)
        错误堆栈 = traceback.format_exc()
        错误日志器.error(f"数据层-详细错误堆栈:\n{错误堆栈}")
        return None

async def 异步创建通告(
    类型: str, 
    标题: str, 
    内容: List[Dict[str, str]], 
    已发布: bool = False, 
    重要性: int = 1, 
    开始时间: Optional[str] = None, 
    结束时间: Optional[str] = None, 
    排序: Optional[int] = None,
    操作标识: Optional[int] = 0
) -> Dict[str, Any]:
    """异步创建新通告

    参数:
    类型: str - 通告类型
    标题: str - 通告标题
    内容: List[Dict[str, str]] - 通告内容，格式为[{"内容": "xxx", "类型": "文本"}, ...]
    已发布: bool - 是否已发布
    重要性: int - 重要性级别：1普通，2重要，3紧急
    开始时间: Optional[str] - 有效期开始时间
    结束时间: Optional[str] - 有效期结束时间
    排序: Optional[int] - 显示排序
    操作标识: Optional[int] - 操作标识，默认为0

    返回值:
    Dict[str, Any]: 包含操作结果的字典，包括成功状态和新通告ID
    """
    try:
        # 验证内容格式
        if not isinstance(内容, list):
            return {"成功": False, "消息": "内容必须是数组格式", "id": None}
        
        # 将内容列表转换为JSON字符串
        内容_json = json.dumps(内容, ensure_ascii=False)
        
        # PostgreSQL环境中假设通告表已存在，使用固定字段列表
        字段列表 = ['id', '类型', '标题', '内容', '已发布', '重要性', '操作标识', '开始时间', '结束时间', '发布时间', '创建时间', '更新时间']
        
        # 检查是否有所有需要的字段
        所需字段 = ['类型', '标题', '内容', '已发布', '重要性', '开始时间', '结束时间', '排序', '操作标识']
        
        # 检查所有需要的字段是否存在
        if all(字段 in 字段列表 for 字段 in 所需字段):
            # 完整字段插入语句
            插入语句 = """
                INSERT INTO 通告 (类型, 标题, 内容, 已发布, 重要性, 开始时间, 结束时间, 操作标识)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """
            # 转换值的类型确保兼容性
            已发布_值 = 1 if 已发布 else 0  # 布尔值转整数
            重要性_值 = int(重要性) if 重要性 is not None else 1
            排序_值 = int(排序) if 排序 is not None else 0 # 处理排序值
            操作标识_值 = int(操作标识) if 操作标识 is not None else 0
            
            # 执行插入
            系统日志器.debug(f"执行通告插入: 类型={类型}, 标题={标题}, 已发布={已发布_值}, 重要性={重要性_值}, 排序={排序_值}, 操作标识={操作标识_值}")

            # 修改插入语句以返回ID
            插入语句_返回ID = 插入语句.rstrip(';') + " RETURNING id"

            插入ID = await 异步连接池实例.执行插入并返回id(
                插入语句_返回ID,
                tuple([类型, 标题, 内容_json, 已发布_值, 重要性_值, 开始时间, 结束时间, 操作标识_值])
            )

            if not 插入ID:
                错误日志器.error(f"创建通告时数据库插入失败: 类型={类型}, 标题={标题}")
                return {"成功": False, "消息": "插入数据库失败", "id": None}

            系统日志器.info(f"成功创建通告: ID={插入ID}, 类型={类型}, 标题={标题}, 已发布={已发布}, 重要性={重要性}")

            return {"成功": True, "消息": "通告创建成功", "id": 插入ID}
        else:
            错误日志器.error(f"通告表缺少所需字段: 当前字段={字段列表}, 所需字段={所需字段}")
            缺失字段 = [字段 for 字段 in 所需字段 if 字段 not in 字段列表]
            return {"成功": False, "消息": f"通告表结构不完整，缺少字段: {', '.join(缺失字段)}", "id": None}
    except json.JSONDecodeError as json_err:
        错误日志器.error(f"创建通告时内容JSON序列化失败: {json_err}", exc_info=True)
        return {"成功": False, "消息": f"内容格式错误: {str(json_err)}", "id": None}
    except Exception as e:
        错误日志器.error(f"异步创建通告失败: {e}", exc_info=True)
        print(f"异步创建通告失败: {e}")
        print(traceback.format_exc())
        return {"成功": False, "消息": f"创建通告时发生错误: {str(e)}", "id": None}

async def 异步更新通告(
    通告id: int, 
    类型: str, 
    标题: str, 
    内容: List[Dict[str, str]], 
    操作标识: Optional[int] = None, 
    已发布: Optional[bool] = None, 
    重要性: Optional[int] = None, 
    开始时间: Optional[datetime] = None, 
    结束时间: Optional[datetime] = None,
    排序: Optional[int] = None
) -> Dict[str, Any]:
    """异步更新现有通告，支持部分字段更新

    参数:
    通告id: int - 通告ID
    类型: str - 通告类型
    标题: str - 通告标题
    内容: List[Dict[str, str]] - 通告内容，格式为[{"内容": "xxx", "类型": "文本"}, ...]
    操作标识: Optional[int] - 操作标识，为None时不更新此字段
    已发布: Optional[bool] - 是否已发布，为None时不更新此字段
    重要性: Optional[int] - 重要性级别，为None时不更新此字段
    开始时间: Optional[datetime] - 有效期开始时间，为None时不更新此字段
    结束时间: Optional[datetime] - 有效期结束时间，为None时不更新此字段
    排序: Optional[int] - 显示排序，为None时不更新此字段

    返回值:
    Dict[str, Any]: 包含操作结果的字典，包括成功状态
    """
    try:
        # 验证内容格式
        if not isinstance(内容, list):
            return {"成功": False, "消息": "内容必须是数组格式"}
        
        # 将内容列表转换为JSON字符串
        内容_json = json.dumps(内容, ensure_ascii=False)
        
        # 动态构建更新语句和参数
        更新字段 = []
        更新参数 = []
        
        # 总是更新基本信息
        参数索引 = 1
        更新字段.append(f"类型 = ${参数索引}")
        更新参数.append(类型)
        参数索引 += 1
        更新字段.append(f"标题 = ${参数索引}")
        更新参数.append(标题)
        参数索引 += 1
        更新字段.append(f"内容 = ${参数索引}")
        更新参数.append(内容_json)
        参数索引 += 1
        
        # 添加可选字段
        if 操作标识 is not None:
            更新字段.append(f"操作标识 = ${参数索引}")
            更新参数.append(操作标识)
            参数索引 += 1
        if 已发布 is not None:
            更新字段.append(f"已发布 = ${参数索引}")
            更新参数.append(已发布)
            参数索引 += 1
            if 已发布:
                # 如果设置为发布，则更新发布时间（仅当发布时间为空时）
                更新字段.append("发布时间 = CASE WHEN 发布时间 IS NULL THEN NOW() ELSE 发布时间 END")
        if 重要性 is not None:
            更新字段.append(f"重要性 = ${参数索引}")
            更新参数.append(重要性)
            参数索引 += 1
        if 开始时间 is not None:
            更新字段.append(f"开始时间 = ${参数索引}")
            更新参数.append(开始时间)
            参数索引 += 1
        if 结束时间 is not None:
            更新字段.append(f"结束时间 = ${参数索引}")
            更新参数.append(结束时间)
            参数索引 += 1
        if 排序 is not None:
            更新字段.append(f"排序 = ${参数索引}")
            更新参数.append(排序)
            参数索引 += 1
        
        # 自动更新"更新时间"字段
        更新字段.append("更新时间 = NOW()")
        
        # 如果没有任何字段需要更新 (理论上不会发生，因为总会更新类型、标题、内容)
        if not 更新字段:
            return {"成功": True, "消息": "没有需要更新的字段"} 
            
        # 构建完整的更新语句
        更新语句 = f"""
            UPDATE 通告
            SET {", ".join(更新字段)}
            WHERE id = ${参数索引}
        """
        
        # 添加通告ID到参数列表最后
        更新参数.append(通告id)
        
        # 执行更新
        影响行数 = await 异步连接池实例.执行更新(更新语句, 更新参数)
        
        if 影响行数 == 0:
            # 检查通告是否存在以提供更明确的错误信息
            通告 = await 异步获取通告详情(通告id)
            if not 通告:
                错误日志器.warning(f"尝试更新不存在的通告: ID={通告id}")
                return {"成功": False, "消息": f"通告ID {通告id} 不存在"}
            else:
                错误日志器.warning(f"更新通告失败，数据库未返回影响行数: ID={通告id}")
                return {"成功": False, "消息": "更新数据库失败，未影响任何行"}
        
        系统日志器.info(f"成功更新通告: ID={通告id}, 更新字段: {', '.join(更新字段)}")
        
        return {"成功": True, "消息": "通告更新成功"}
    except json.JSONDecodeError as json_err:
        错误日志器.error(f"更新通告时内容JSON序列化失败: {json_err}", exc_info=True)
        return {"成功": False, "消息": f"内容格式错误: {str(json_err)}"}
    except Exception as e:
        错误日志器.error(f"异步更新通告失败: {e}", exc_info=True)
        print(f"异步更新通告失败: {e}")
        print(traceback.format_exc())
        return {"成功": False, "消息": f"更新通告时发生错误: {str(e)}"}

async def 异步删除通告(通告id: int) -> Dict[str, Any]:
    """异步删除通告

    参数:
    通告id: int - 要删除的通告ID

    返回值:
    Dict[str, Any]: 包含操作结果的字典，包括成功状态
    """
    try:
        # 删除数据
        删除语句 = "DELETE FROM 通告 WHERE id = $1"
        # 使用执行更新方法代替执行查询方法，因为DELETE操作应该返回影响的行数
        影响行数 = await 异步连接池实例.执行更新(删除语句, [通告id])
        
        if 影响行数 == 0:
             错误日志器.warning(f"尝试删除不存在或删除失败的通告: ID={通告id}")
             return {"成功": False, "消息": f"删除失败，通告ID {通告id} 可能不存在"}
        
        系统日志器.info(f"成功删除通告: ID={通告id}")
        
        return {"成功": True, "消息": "通告删除成功"}
    except Exception as e:
        错误日志器.error(f"异步删除通告失败: {e}", exc_info=True)
        print(f"异步删除通告失败: {e}")
        print(traceback.format_exc())
        return {"成功": False, "消息": f"删除通告时发生错误: {str(e)}"}

# 测试代码，仅在直接运行该脚本时执行
if __name__ == "__main__":
    import asyncio
    
    async def 测试():
        # 测试获取通告列表
        通告列表结果 = await 异步获取通告列表(页码=1, 每页数量=5)
        print("通告列表结果:")
        print(通告列表结果)
        
        # 如果有通告，测试获取第一个通告的详情
        if 通告列表结果['列表']:
            首个通告id = 通告列表结果['列表'][0]['id']
            通告详情 = await 异步获取通告详情(首个通告id)
            print(f"\n通告 {首个通告id} 详情:")
            print(通告详情)
    
    # 运行异步测试函数
    asyncio.run(测试()) 
