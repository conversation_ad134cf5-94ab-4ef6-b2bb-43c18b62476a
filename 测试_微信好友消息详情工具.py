#!/usr/bin/env python3
"""
直接测试微信好友消息详情工具
验证工具本身的功能是否正常
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from 服务.异步微信服务 import 异步查询微信好友消息详情服务


async def 测试微信好友消息详情工具():
    """直接测试微信好友消息详情工具"""
    print("=" * 60)
    print("微信好友消息详情工具直接测试")
    print("=" * 60)
    print()

    # 测试参数 - 使用用户指定的参数
    用户id = 3
    我方微信号id = 6
    识别id = 1

    print("🔍 测试参数:")
    print(f"   用户id: {用户id}")
    print(f"   我方微信号ID: {我方微信号id}")
    print(f"   识别ID: {识别id}")
    print()

    try:
        # 测试1: 直接调用服务层函数
        print("📋 测试1: 直接调用异步微信服务...")
        服务结果 = await 异步查询微信好友消息详情服务(用户id, 我方微信号id, 识别id)

        print("📊 服务层结果:")
        print(f"   状态: {服务结果.get('status')}")
        print(f"   消息: {服务结果.get('message', 'N/A')}")

        if 服务结果.get("data"):
            数据 = 服务结果["data"]
            print("   数据内容:")
            print(f"     对方微信号: {数据.get('对方微信号', 'N/A')}")
            print(f"     对方昵称: {数据.get('对方昵称', 'N/A')}")
            print(f"     我方微信号: {数据.get('我方微信号', 'N/A')}")
            print(f"     识别id: {数据.get('识别id', 'N/A')}")
            print(
                f"     对方最后消息时间: {数据.get('对方最后一条消息发送时间', 'N/A')}"
            )
            print(
                f"     我方最后消息时间: {数据.get('我方最后一条消息发送时间', 'N/A')}"
            )
            print(f"     好友入库时间: {数据.get('好友入库时间', 'N/A')}")
        print()

        # 测试2: 通过工具包装器调用
        print("📋 测试2: 通过LangChain工具包装器调用...")

        # 首先初始化工具包装器
        from 服务.LangChain_内部函数包装器 import 内部函数包装器实例

        await 内部函数包装器实例.初始化()

        # 获取工具函数
        工具函数 = 内部函数包装器实例.已注册工具.get("查询微信好友对话消息详情")

        if 工具函数:
            print("✅ 找到工具函数")

            # 调用工具函数 - 使用LangChain工具的正确调用方式
            try:
                # 方式1: 使用invoke方法
                工具结果 = await 工具函数.ainvoke(
                    {"用户id": 用户id, "我方微信号id": 我方微信号id, "识别id": 识别id}
                )
            except Exception as e1:
                try:
                    # 方式2: 直接调用原始函数
                    工具结果 = await 工具函数.func(用户id, 我方微信号id, 识别id)
                except Exception as e2:
                    print(f"   调用失败: 方式1={str(e1)}, 方式2={str(e2)}")
                    工具结果 = f"调用失败: {str(e1)}"

            print("📊 工具调用结果:")
            print(f"   结果类型: {type(工具结果)}")
            print(f"   结果长度: {len(str(工具结果))}")
            print("   结果内容:")
            print(f"   {工具结果}")

            # 检查结果格式
            if "好友基本信息" in str(工具结果) and "消息时间信息" in str(工具结果):
                print("✅ 工具返回格式正确，包含好友基本信息和消息时间信息")
            else:
                print("⚠️ 工具返回格式异常")

        else:
            print("❌ 未找到工具函数")
            print("   可用工具列表:")
            for 工具名 in 内部函数包装器实例.已注册工具.keys():
                print(f"     - {工具名}")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()

    print()
    print("=" * 60)
    print("测试完成")
    print("=" * 60)


async def 测试不同参数组合():
    """测试不同的参数组合"""
    print("\n🔬 测试不同参数组合...")

    测试用例 = [
        {"用户id": 3, "我方微信号id": 6, "识别id": 1, "描述": "用户指定参数"},
        {"用户id": 3, "我方微信号id": 4, "识别id": 1, "描述": "不同微信号"},
        {"用户id": 3, "我方微信号id": 6, "识别id": 999, "描述": "不存在的识别id"},
        {"用户id": 999, "我方微信号id": 6, "识别id": 1, "描述": "不存在的用户id"},
        {"用户id": 0, "我方微信号id": 6, "识别id": 1, "描述": "无效用户id"},
        {"用户id": 3, "我方微信号id": 0, "识别id": 1, "描述": "无效微信号id"},
        {"用户id": 3, "我方微信号id": 6, "识别id": 0, "描述": "无效识别id"},
    ]

    for i, 测试用例 in enumerate(测试用例, 1):
        print(f"\n📋 测试用例 {i}: {测试用例['描述']}")
        print(
            f"   参数: 用户id={测试用例['用户id']}, 我方微信号id={测试用例['我方微信号id']}, 识别id={测试用例['识别id']}"
        )

        try:
            结果 = await 异步查询微信好友消息详情服务(
                测试用例["用户id"], 测试用例["我方微信号id"], 测试用例["识别id"]
            )

            print(
                f"   结果: 状态={结果.get('status')}, 消息={结果.get('message', 'N/A')}"
            )

            if 结果.get("data"):
                print(f"   数据: 对方微信号={结果['data'].get('对方微信号', 'N/A')}")

        except Exception as e:
            print(f"   异常: {str(e)}")


async def main():
    """主函数"""
    await 测试微信好友消息详情工具()
    await 测试不同参数组合()


if __name__ == "__main__":
    asyncio.run(main())
